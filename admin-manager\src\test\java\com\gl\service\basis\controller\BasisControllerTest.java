package com.gl.service.basis.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gl.framework.security.service.TokenService;
import com.gl.framework.web.response.Result;
import com.gl.service.basis.entity.BaseAbout;
import com.gl.service.basis.entity.BaseService;
import com.gl.service.basis.service.BasisService;
import com.gl.service.basis.vo.BasisDto;
import com.gl.service.basis.vo.BasisVo;
import com.gl.service.opus.entity.BackgroundMusicType;
import com.gl.service.opus.entity.TemplateType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.context.annotation.Import;
import com.gl.config.TestRedisConfig;
import com.gl.framework.common.util.redis.RedisUtils;
import com.gl.redis.RedisService;
import com.gl.system.service.SysConfigService;
import com.gl.system.service.SysUserService;
import com.gl.framework.security.service.UserDetailsServiceImpl;
import com.gl.service.shop.service.WeChatService;

import java.util.Arrays;
import java.util.Collections;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * BasisController单元测试类
 * 测试基础设置控制器的所有HTTP端点，包括正面和负面场景
 *
 * @author: Test
 * @date: 2024/12/19
 * @version: 1.0
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = com.gl.ManagerApplication.class, properties = {
        "spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration,org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration"
})
@TestPropertySource(locations = "classpath:application-test.yml")
@ActiveProfiles("test")
@AutoConfigureMockMvc
@Import(TestRedisConfig.class)
@DisplayName("BasisController单元测试")
class BasisControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private BasisService basisService;

    @org.springframework.boot.test.mock.mockito.MockBean(name = "ps")
    private com.gl.framework.security.service.PermissionService permissionService;

    @org.springframework.boot.test.mock.mockito.MockBean
    private com.gl.framework.security.service.TokenService tokenService;

    @MockBean
    private StringRedisTemplate stringRedisTemplate;

    // Mock the services in the dependency chain that are causing issues
    @MockBean
    private RedisUtils redisUtils;

    @MockBean
    private RedisService redisService;

    @MockBean
    private SysConfigService sysConfigService;

    @MockBean
    private SysUserService sysUserService;

    @MockBean
    private UserDetailsServiceImpl userDetailsService;

    @MockBean
    private WeChatService weChatService;

    @Autowired
    private ObjectMapper objectMapper;

    private BasisVo mockBasisVo;
    private BasisDto mockBasisDto;
    private Result successResult;
    private Result failResult;

    @BeforeEach
    void setUp() {
        // 配置权限服务模拟，根据权限字符串返回不同结果
        when(permissionService.hasPermi("basis:basis:list")).thenReturn(true);
        when(permissionService.hasPermi("basis:basis:add")).thenReturn(true);
        when(permissionService.hasPermi("basis:basis:template")).thenReturn(true);
        when(permissionService.hasPermi("basis:basis:music")).thenReturn(true);
        when(permissionService.hasPermi("other:permission")).thenReturn(false);
        // 对于任何其他权限，默认返回false
        when(permissionService.hasPermi(argThat(perm -> !perm.equals("basis:basis:list") &&
                !perm.equals("basis:basis:add") &&
                !perm.equals("basis:basis:template") &&
                !perm.equals("basis:basis:music")))).thenReturn(false);

        // 配置Redis模板模拟，避免实际Redis连接
        // 由于已经通过@MockBean注解模拟了StringRedisTemplate，这里不需要额外配置
        // 初始化测试数据
        BaseService baseService = new BaseService();
        baseService.setId(1L);
        baseService.setWechat("test_wechat");
        baseService.setPhone("13800138000");

        BaseAbout baseAbout = new BaseAbout();
        baseAbout.setId(1L);
        baseAbout.setContent("关于我们的内容");

        TemplateType templateType = new TemplateType();
        templateType.setId(1L);
        templateType.setName("测试模板类型");

        BackgroundMusicType musicType = new BackgroundMusicType();
        musicType.setId(1L);
        musicType.setName("测试音乐类型");

        mockBasisVo = new BasisVo();
        mockBasisVo.setBaseService(baseService);
        mockBasisVo.setBaseAbout(baseAbout);
        mockBasisVo.setTemplateTypes(Arrays.asList(templateType));
        mockBasisVo.setBackgroundMusicTypes(Arrays.asList(musicType));

        mockBasisDto = new BasisDto();
        mockBasisDto.setTemplateTypeId(1L);
        mockBasisDto.setMusicTypeId(1L);

        successResult = Result.success(mockBasisVo);
        failResult = Result.fail("操作失败");
    }

    @Test
    @DisplayName("查询基础设置信息 - 成功场景")
    @WithMockUser(authorities = "basis:basis:list")
    void testSelect_Success() throws Exception {
        // Given
        when(basisService.select()).thenReturn(successResult);

        // When & Then
        mockMvc.perform(get("/basis")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(10000))
                .andExpect(jsonPath("$.message").value("success"))
                .andExpect(jsonPath("$.data").exists())
                .andExpect(jsonPath("$.data.baseService").exists())
                .andExpect(jsonPath("$.data.baseAbout").exists())
                .andExpect(jsonPath("$.data.templateTypes").isArray())
                .andExpect(jsonPath("$.data.backgroundMusicTypes").isArray());

        verify(basisService).select();
    }

    @Test
    @DisplayName("查询基础设置信息 - 无权限场景")
    @WithMockUser(authorities = "other:permission")
    void testSelect_NoPermission() throws Exception {
        // When & Then
        mockMvc.perform(get("/basis")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(40001));

        verify(basisService, never()).select();
    }

    @Test
    @DisplayName("查询基础设置信息 - 未登录场景")
    void testSelect_NotAuthenticated() throws Exception {
        // When & Then
        mockMvc.perform(get("/basis")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(40001));

        verify(basisService, never()).select();
    }

    @Test
    @DisplayName("查询基础设置信息 - 服务异常场景")
    @WithMockUser(authorities = "basis:basis:list")
    void testSelect_ServiceException() throws Exception {
        // Given
        when(basisService.select()).thenThrow(new RuntimeException("数据库连接失败"));

        // When & Then
        mockMvc.perform(get("/basis")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(10001));

        verify(basisService).select();
    }

    @Test
    @DisplayName("添加基础设置信息 - 成功场景")
    @WithMockUser(authorities = "basis:basis:add")
    void testAdd_Success() throws Exception {
        // Given
        when(basisService.add(any(BasisVo.class))).thenReturn(Result.success());

        // When & Then
        mockMvc.perform(post("/basis")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockBasisVo)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(10000))
                .andExpect(jsonPath("$.message").value("success"));

        verify(basisService).add(any(BasisVo.class));
    }

    @Test
    @DisplayName("添加基础设置信息 - 请求体为空场景")
    @WithMockUser(authorities = "basis:basis:add")
    void testAdd_EmptyRequestBody() throws Exception {
        // When & Then
        mockMvc.perform(post("/basis")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content("{}"))
                .andDo(print())
                .andExpect(status().isOk());

        verify(basisService).add(any(BasisVo.class));
    }

    @Test
    @DisplayName("添加基础设置信息 - 无效JSON格式场景")
    @WithMockUser(authorities = "basis:basis:add")
    void testAdd_InvalidJson() throws Exception {
        // When & Then
        mockMvc.perform(post("/basis")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content("invalid json"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10001));

        verify(basisService, never()).add(any(BasisVo.class));
    }

    @Test
    @DisplayName("添加基础设置信息 - 无权限场景")
    @WithMockUser(authorities = "other:permission")
    void testAdd_NoPermission() throws Exception {
        // When & Then
        mockMvc.perform(post("/basis")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockBasisVo)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(40001));

        verify(basisService, never()).add(any(BasisVo.class));
    }

    @Test
    @DisplayName("添加基础设置信息 - 缺少CSRF Token场景")
    @WithMockUser(authorities = "basis:basis:add")
    void testAdd_MissingCsrfToken() throws Exception {
        // When & Then
        mockMvc.perform(post("/basis")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockBasisVo)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(40001));

        verify(basisService, never()).add(any(BasisVo.class));
    }

    @Test
    @DisplayName("添加基础设置信息 - 服务返回失败结果场景")
    @WithMockUser(authorities = "basis:basis:add")
    void testAdd_ServiceReturnsFail() throws Exception {
        // Given
        when(basisService.add(any(BasisVo.class))).thenReturn(failResult);

        // When & Then
        mockMvc.perform(post("/basis")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockBasisVo)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(10001))
                .andExpect(jsonPath("$.message").value("操作失败"));

        verify(basisService).add(any(BasisVo.class));
    }

    @Test
    @DisplayName("删除模板类型 - 成功场景")
    @WithMockUser(authorities = "basis:basis:template")
    void testDeleteTemplate_Success() throws Exception {
        // Given
        when(basisService.deleteTemplate(eq(1L))).thenReturn(Result.success());

        // When & Then
        mockMvc.perform(delete("/basis/template")
                .param("templateTypeId", "1"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(10000))
                .andExpect(jsonPath("$.message").value("success"));

        verify(basisService).deleteTemplate(1L);
    }

    @Test
    @DisplayName("删除模板类型 - 参数为空场景")
    @WithMockUser(authorities = "basis:basis:template")
    void testDeleteTemplate_NullParameter() throws Exception {
        // Given
        when(basisService.deleteTemplate(null)).thenReturn(Result.fail("模板类型id不能为空"));

        // When & Then
        mockMvc.perform(delete("/basis/template"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(10001))
                .andExpect(jsonPath("$.message").value("模板类型id不能为空"));

        verify(basisService).deleteTemplate(null);
    }

    @Test
    @DisplayName("删除模板类型 - 无权限场景")
    @WithMockUser(authorities = "other:permission")
    void testDeleteTemplate_NoPermission() throws Exception {
        // When & Then
        mockMvc.perform(delete("/basis/template")
                .param("templateTypeId", "1"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(40001));

        verify(basisService, never()).deleteTemplate(any());
    }

    @Test
    @DisplayName("删除背景音乐类型 - 成功场景")
    @WithMockUser(authorities = "basis:basis:music")
    void testDeleteMusic_Success() throws Exception {
        // Given
        when(basisService.deleteMusic(eq(1L))).thenReturn(Result.success());

        // When & Then
        mockMvc.perform(delete("/basis/music")
                .param("musicTypeId", "1"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(10000))
                .andExpect(jsonPath("$.message").value("success"));

        verify(basisService).deleteMusic(1L);
    }

    @Test
    @DisplayName("删除背景音乐类型 - 参数为空场景")
    @WithMockUser(authorities = "basis:basis:music")
    void testDeleteMusic_NullParameter() throws Exception {
        // Given
        when(basisService.deleteMusic(null)).thenReturn(Result.fail("背景音乐类型不能为空"));

        // When & Then
        mockMvc.perform(delete("/basis/music"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(10001))
                .andExpect(jsonPath("$.message").value("背景音乐类型不能为空"));

        verify(basisService).deleteMusic(null);
    }

    @Test
    @DisplayName("删除背景音乐类型 - 无权限场景")
    @WithMockUser(authorities = "other:permission")
    void testDeleteMusic_NoPermission() throws Exception {
        // When & Then
        mockMvc.perform(delete("/basis/music")
                .param("musicTypeId", "1"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(40001));

        verify(basisService, never()).deleteMusic(any());
    }

    @Test
    @DisplayName("删除背景音乐类型 - 服务异常场景")
    @WithMockUser(authorities = "basis:basis:music")
    void testDeleteMusic_ServiceException() throws Exception {
        // Given
        when(basisService.deleteMusic(eq(1L))).thenThrow(new RuntimeException("数据库操作失败"));

        // When & Then
        mockMvc.perform(delete("/basis/music")
                .param("musicTypeId", "1"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(10001));

        verify(basisService).deleteMusic(1L);
    }

    @Test
    @DisplayName("添加基础设置信息 - 服务异常场景")
    @WithMockUser(authorities = "basis:basis:add")
    void testAdd_ServiceException() throws Exception {
        // Given
        when(basisService.add(any(BasisVo.class))).thenThrow(new RuntimeException("数据库保存失败"));

        // When & Then
        mockMvc.perform(post("/basis")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockBasisVo)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(10001));

        verify(basisService).add(any(BasisVo.class));
    }

    @Test
    @DisplayName("删除模板类型 - 服务异常场景")
    @WithMockUser(authorities = "basis:basis:template")
    void testDeleteTemplate_ServiceException() throws Exception {
        // Given
        when(basisService.deleteTemplate(eq(1L))).thenThrow(new RuntimeException("数据库更新失败"));

        // When & Then
        mockMvc.perform(delete("/basis/template")
                .param("templateTypeId", "1"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(10001));

        verify(basisService).deleteTemplate(1L);
    }

    @Test
    @DisplayName("添加基础设置信息 - 大数据量场景")
    @WithMockUser(authorities = "basis:basis:add")
    void testAdd_LargeData() throws Exception {
        // Given
        BasisVo largeBasisVo = new BasisVo();
        largeBasisVo.setBaseService(mockBasisVo.getBaseService());
        largeBasisVo.setBaseAbout(mockBasisVo.getBaseAbout());

        // 创建大量模板类型数据
        java.util.List<TemplateType> largeTemplateTypes = new java.util.ArrayList<>();
        for (int i = 0; i < 100; i++) {
            TemplateType templateType = new TemplateType();
            templateType.setId((long) i);
            templateType.setName("模板类型" + i);
            largeTemplateTypes.add(templateType);
        }
        largeBasisVo.setTemplateTypes(largeTemplateTypes);
        largeBasisVo.setBackgroundMusicTypes(Collections.emptyList());

        when(basisService.add(any(BasisVo.class))).thenReturn(Result.success());

        // When & Then
        mockMvc.perform(post("/basis")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(largeBasisVo)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(10000));

        verify(basisService).add(any(BasisVo.class));
    }

    @Test
    @DisplayName("查询基础设置信息 - 返回空数据场景")
    @WithMockUser(authorities = "basis:basis:list")
    void testSelect_EmptyData() throws Exception {
        // Given
        BasisVo emptyBasisVo = new BasisVo();
        emptyBasisVo.setBaseService(null);
        emptyBasisVo.setBaseAbout(null);
        emptyBasisVo.setTemplateTypes(Collections.emptyList());
        emptyBasisVo.setBackgroundMusicTypes(Collections.emptyList());

        Result emptyResult = Result.success(emptyBasisVo);
        when(basisService.select()).thenReturn(emptyResult);

        // When & Then
        mockMvc.perform(get("/basis")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(10000))
                .andExpect(jsonPath("$.data.baseService").doesNotExist())
                .andExpect(jsonPath("$.data.baseAbout").doesNotExist())
                .andExpect(jsonPath("$.data.templateTypes").isEmpty())
                .andExpect(jsonPath("$.data.backgroundMusicTypes").isEmpty());

        verify(basisService).select();
    }

    @Test
    @DisplayName("删除模板类型 - 无效ID格式场景")
    @WithMockUser(authorities = "basis:basis:template")
    void testDeleteTemplate_InvalidIdFormat() throws Exception {
        // When & Then
        mockMvc.perform(delete("/basis/template")
                .param("templateTypeId", "invalid"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000));

        verify(basisService).deleteTemplate(0L);
    }

    @Test
    @DisplayName("删除背景音乐类型 - 无效ID格式场景")
    @WithMockUser(authorities = "basis:basis:music")
    void testDeleteMusic_InvalidIdFormat() throws Exception {
        // When & Then
        mockMvc.perform(delete("/basis/music")
                .param("musicTypeId", "invalid"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000));

        verify(basisService).deleteMusic(0L);
    }

    @Test
    @DisplayName("添加基础设置信息 - 部分数据为null场景")
    @WithMockUser(authorities = "basis:basis:add")
    void testAdd_PartialNullData() throws Exception {
        // Given
        BasisVo partialBasisVo = new BasisVo();
        partialBasisVo.setBaseService(mockBasisVo.getBaseService());
        partialBasisVo.setBaseAbout(null);
        partialBasisVo.setTemplateTypes(null);
        partialBasisVo.setBackgroundMusicTypes(Collections.emptyList());

        when(basisService.add(any(BasisVo.class))).thenReturn(Result.success());

        // When & Then
        mockMvc.perform(post("/basis")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(partialBasisVo)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(10000));

        verify(basisService).add(any(BasisVo.class));
    }

    @Test
    @DisplayName("HTTP方法不支持场景 - PUT请求")
    @WithMockUser(authorities = "basis:basis:add")
    void testUnsupportedHttpMethod_Put() throws Exception {
        // When & Then
        mockMvc.perform(put("/basis")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(mockBasisVo)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10001));

        verify(basisService, never()).add(any(BasisVo.class));
    }

    @Test
    @DisplayName("HTTP方法不支持场景 - PATCH请求")
    @WithMockUser(authorities = "basis:basis:list")
    void testUnsupportedHttpMethod_Patch() throws Exception {
        // When & Then
        mockMvc.perform(patch("/basis")
                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10001));

        verify(basisService, never()).select();
    }
}
